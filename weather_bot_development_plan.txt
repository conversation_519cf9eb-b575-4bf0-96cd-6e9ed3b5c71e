
Данный файл представляет собой подробный план по разработке Telegram‑бота, который автоматически публикует и обновляет прогнозы погоды по ключевому слову «weather» в канале. План содержит информацию о необходимых API, архитектуре проекта, предлагаемых модулях и последовательности работ. Все параметры и шаги описаны максимально подробно.

## 1. Описание задачи

Нужно разработать Telegram‑бота, которого можно добавить в канал. Если любой участник канала публикует пост с ключевым словом **«weather»**, бот должен создать и разместить три отдельных сообщения (порядок строго задан):

1. **Погода на неделю** – общий прогноз на 7 дней по районам Гагаринский, Севастополь. Сообщение содержит минимальные сведения для каждого дня: дата, краткое описание погодных условий, дневная и ночная температуры в °C, ощущаемая температура, скорость ветра. Восход/закат в этом сообщении не указываются.
2. **Погода на сегодня** – разбивка на четыре периода: утро, день, вечер и ночь. Для каждого периода указываются температура, ощущаемая температура, ветер, краткий текст с описанием погоды. В конце сообщения выводятся время восхода и заката солнца; если восход уже прошёл, показывается время восхода на следующий день. Указывать эти данные нужно в Московском времени (MSK; UTC+3).
3. **Погода сейчас** – короткое сообщение, отражающее текущие погодные условия: температура, как ощущается, скорость и направление ветра, краткое описание состояния неба. В этом сообщении восход/закат не перечисляются.

После публикации бот должен **обновлять** сообщения с заданной периодичностью:

* Прогноз на неделю — каждые **12 часов**.
* Прогноз на сегодня — **ежечасно**.
* Погода сейчас — каждые **10 минут**.

Обновление должно происходить через редактирование существующих сообщений (не создавая новых) с сохранением изначального порядка публикации. Бот должен всегда запрашивать свежие данные из источников, обновлять описание и менять изображение, если рекомендованный ИИ‑моделью снимок изменился.

## 2. Используемые API и требования

### 2.1 OpenWeatherMap One Call API 3.0

* **Назначение** – получение метеоданных. Будут использоваться следующие части ответа: `current` (текущая погода), `hourly` (почасовой прогноз на 48 ч) и `daily` (прогноз на 7 дней). Запросы будут выполняться по координатам Гагаринского района Севастополя (примерные координаты: широта **44.600° N**, долгота **33.517° E**)https://en.wikipedia.org/wiki/Gagarinsky_District,_Sevastopol#:~:text=Coordinates%3A%2044%C2%B036%E2%80%B2N%2033%C2%B031%E2%80%B2E%20%EF%BB%BF%20%2F,517. Эти координаты следует зашить в конфигурационный файл.
* **Формат запроса** – `https://api.openweathermap.org/data/3.0/onecall?lat={lat}&lon={lon}&exclude={parts}&units=metric&lang=ru&appid={API_key}`. В запросе:
  * `lat` и `lon` – координаты местности (44.600, 33.517);
  * `exclude` – список частей, которые не нужны (например, `minutely,alerts`);
  * `units=metric` – чтобы получать значения температуры в градусах Цельсияhttps://openweathermap.org/api/one-call-3#:~:text=How%20to%20make%20an%20API,call;
  * `lang=ru` – для получения описаний погоды на русском языке;
  * `appid` – ключ API.
* **Ответ** – JSON, включающий объекты `current`, `hourly`, `daily` с полями:
  * `weather[0].description` – краткое текстовое описание состояния небаhttps://openweathermap.org/api/one-call-3;
  * `temp` и `feels_like` – фактическая и ощущаемая температура в `current` и `hourly`, а также вложенный объект `temp` с ключами `morn`, `day`, `eve`, `night` для суточных данных в `daily`;
  * `wind_speed` – скорость ветра (м/с);
  * `sunrise` и `sunset` – время восхода и заката в Unix‑формате (секунды), пригодное для конвертации в локальную зонуhttps://openweathermap.org/api/one-call-3.
* **Единицы измерения** – по умолчанию API возвращает температуру в кельвинах, для Цельсия нужно явно указать `units=metric`https://openweathermap.org/faq#:~:text=How%20can%20I%20switch%20between,temperature%20units%20in%20API%20calls.

### 2.2 Cerebras Generative API (модель Qwen‑3‑235B‑A22B‑Instruct‑2507)

* **Назначение** – выбор подходящего изображения для каждого сообщения с погодой. Бот будет передавать модельке список названий доступных картинок (например, `sunny.jpg`, `cloudy.jpg`, `rainy.jpg` и т.д.) и текущий момент времени в формате «dd.MM.yyyy HH:MM (MSK)» в запросе. Модель должна выбрать название одной картинки, которое лучше всего описывает текущие погодные условия. Ответ модели содержит только строку с названием файла – его нужно использовать в качестве изображения в сообщении.
* **Эндпойнт** – `https://api.cerebras.ai/v1/chat/completions`, согласно примеру вызова на официальном форуме: в cURL запросе передаётся заголовок `Authorization: Bearer {API_KEY}` и JSON‑тело с полями `model`, `messages`, `stream` и др.https://community.home-assistant.io/t/solved-cerebras-llama-3-3-token-length-150-exceeded-increase-maximum-token-to-avoid-the-issue/848932#:~:text=Don%E2%80%99t%20think%20so,works%20even%20for%20larger%20replys. Аналогичный пример приведён в документации AI Gateway Cloudflare – `curl https://gateway.ai.cloudflare.com/v1/{account_id}/{gateway_id}/cerebras/chat/completions -H 'content-type: application/json' -H 'Authorization: Bearer CEREBRAS_TOKEN' -d '{...}'`https://developers.cloudflare.com/ai-gateway/providers/cerebras/#:~:text=https%3A%2F%2Fgateway.ai.cloudflare.com%2Fv1%2F%7Baccount_id%7D%2F%7Bgateway_id%7D%2Fcerebras.
* **Параметры запроса**:
  * `model` – строго `qwen-3-235b-a22b-instruct-2507`;
  * `messages` – список объектов вида `{ "role": "user", "content": <текст запроса> }`;
  * `temperature`, `top_p`, `max_tokens` – можно установить на усмотрение разработчика (например, 0 для детерминированного выбора);
  * `stream` – false (нужен полный ответ, а не поток);
  * API‑ключ: `csk-cnjd9w9nx5kjtpj99936fexmhpxdc586ny3k6rc96rfn4rkw` (по заданию ключ встроен в код, в файл с конфигурацией).
* **Формат запроса** – мы можем использовать `requests` или `httpx` (async) для отправки POST‑запросов. Обязательные заголовки: `Content-Type: application/json`, `Authorization: Bearer <API_KEY>`.
* **Пример тела запроса**:
```
{
  "model": "qwen-3-235b-a22b-instruct-2507",
  "messages": [
    {
      "role": "user",
      "content": "Сейчас 30.07.2025 12:00 (MSK). Выбери лучшую картинку для пасмурного дня. Доступные: sunny.jpg, cloudy.jpg, rainy.jpg. Ответь только названием файла."
    }
  ],
  "temperature": 0,
  "top_p": 1,
  "max_tokens": 1,
  "stream": false
}
```
* **Формат ответа** – объект `choices` с сообщением (`choices[0].message.content`), содержащим выбранное название картинки. Бот должен извлечь эту строку и использовать как путь к изображению (файлы изображений должны быть размещены в папке проекта).

### 2.3 Telegram Bot API

* **Метод sendPhoto** – позволяет отправлять фотографию с подписью в чат или канал. Параметры: `chat_id` (идентификатор канала), `photo` (файл, URL или file_id), `caption` (текст до 1024 символов), `parse_mode` (HTML или Markdown), `disable_notification`, `reply_markup` и др. Подробное описание: для отправки изображения достаточно указать `chat_id`, `photo`, при необходимости – `caption` и `parse_mode`; Telegram возвращает объект Messagehttps://core.telegram.org/bots/api#:~:text=.
* **Метод sendMessage** – используется, если нужно отправить текст без медиа. Обязательные параметры: `chat_id` и `text`. Есть опции `parse_mode`, `link_preview_options`, `disable_notification` и др. Возвращает объект Messagehttps://core.telegram.org/bots/api#:~:text=. В нашем проекте отправка изображений происходит через `sendPhoto`; `sendMessage` может быть полезен для сообщений без картинок.
* **Редактирование сообщений**:
  * **editMessageText** – используется для изменения текста (или подписи к фотографии) ранее отправленного сообщения. Работает с параметрами `chat_id`, `message_id`, `text`, `parse_mode` и др. Aiogram‑документация указывает, что метод используется для редактирования текстовых и игровых сообщений; для редактирования бизнес‑сообщений действуют ограничения (в нашем случае сообщения обычные)https://docs.aiogram.dev/en/latest/api/methods/edit_message_text.html#:~:text=.
  * **editMessageMedia** – используется для замены медиафайла (фото, видео, документ) в сообщении. При редактировании сообщения, отправленного ботом в канал, нужно указать `chat_id`, `message_id` и объект `media` (InputMediaPhoto с новым `media` и новым `caption`); ответы содержат изменённый объект Message. Aiogram‑документация отмечает, что при редактировании медиа должно использоваться уже загруженное изображение (file_id) или URL; новая загрузка файла через этот метод невозможнаhttps://docs.aiogram.dev/en/latest/api/methods/edit_message_media.html#:~:text=Use%20this%20method%20to%20edit,the%20time%20they%20were%20senthttps://docs.aiogram.dev/en/latest/api/methods/edit_message_media.html#:~:text=chat_id%3A%20ChatIdUnion%20. Поэтому для обновлений мы будем отправлять новое фото заранее с помощью `sendPhoto`, а затем в редактировании будем указывать file_id, полученный при первой отправке.
* **JobQueue / планировщик** – библиотека python‑telegram‑bot предоставляет класс `JobQueue`, который позволяет планировать периодические задачи. Метод `run_repeating(callback, interval, first=None, last=None, context=None, name=None)` создаёт новую задачу, выполняющуюся с заданным интервалом (в секундах), начиная с момента, заданного в `first`. Описание параметров: `callback` – функция, принимающая `CallbackContext`; `interval` – интервал в секундах или объект timedelta; `first` – время первой запуска задачи (может быть число секунд, timedelta, datetime или time); возвращается экземпляр Jobython-telegram-bot.readthedocs.io/_/downloads/en/v13.2/pdf/#:~:text=run_once%28callback%3A%20Callable,Reference. Аналогично, метод `run_once(callback, when, context=None, name=None)` планирует однократное выполнение задачиython-telegram-bot.readthedocs.io/_/downloads/en/v13.2/pdf/#:~:text=run_once%28callback%3A%20Callable,Reference. Эти методы пригодятся для запуска обновления прогнозов по заданному расписанию.

## 3. Технологический стек

* **Язык**: Python 3.11+.
* **Библиотеки для Telegram**: `python-telegram-bot` (версия 21 или выше) для асинхронной работы с Bot API и использования JobQueue. Возможно использовать альтернативу `aiogram`, но `python-telegram-bot` удобнее из‑за встроенного планировщика.
* **HTTP‑клиент**: `aiohttp` или `httpx` для асинхронных запросов к OpenWeatherMap и Cerebras. Можно использовать и `requests` в синхронном варианте, если бот будет работать в отдельном потоке.
* **Парсинг и форматирование даты/времени**: стандартный модуль `datetime`, библиотека `zoneinfo` (Python 3.9+) для работы с часовыми поясами. Пример конвертации времени в другой часовой пояс представлен на Stack Overflow: сначала локализуем метку времени в исходной зоне, затем вызываем `.astimezone()` с нужной зонойhttps://stackoverflow.com/questions/31977563/how-do-you-convert-a-datetime-timestamp-from-one-timezone-to-another-timezone#:~:text=1.%20Make%20two%20,import%20pytz%3B%20pytz.all_timezones. В нашем случае следует конвертировать `sunrise` и `sunset` в часовой пояс MSK (UTC+3).
* **Сохранение состояния**: для хранения `message_id` опубликованных сообщений, временных меток обновления, списков изображений можно использовать простой файл JSON или SQLite‑базу. Для минималистичного решения подойдёт JSON; библиотека `shelve` или `tinydb` может упростить доступ.
* **Логирование**: `logging` из стандартной библиотеки Python для записи ошибок и событий.
* **Утилиты**: `python-dotenv` (опционально) для загрузки переменных окружения из `.env`.

## 4. Предлагаемая структура проекта (файлы)

Все файлы располагаются в одной папке. Названия файлов начинаются с `weather_` для очевидности. Количество файлов не превышает десяти.

1. **weather_main.py** – точка входа приложения. Настраивает логирование, загружает конфигурацию, инициализирует объект Bot и JobQueue, регистрирует обработчики обновлений (`channel_post`), запускает планировщик и запускает приложение.
2. **weather_config.py** – содержит константы и настройки: токен бота, API‑ключи OpenWeatherMap и Cerebras, координаты местоположения, часовой пояс (Europe/Moscow), интервалы обновления (в секундах), список доступных изображений с путями и названиями, ID канала. При необходимости значения читаются из переменных окружения.
3. **weather_api.py** – модуль для работы с OpenWeatherMap API. Содержит функции `fetch_weather()` (получает JSON для нужного периода), `parse_current()`, `parse_today()`, `parse_week()` (разбирают данные и возвращают структуры с нужными полями), а также функцию для конвертации времени восхода/заката в строку по МСК.
4. **weather_cerebras.py** – функции для взаимодействия с Cerebras: `select_image(description, available_images, current_time)` отправляет запрос с описанием погоды, текущим временем и списком файлов, получает ответ от модели и возвращает название картинки; внутри реализована обработка ошибок.
5. **weather_utils.py** – вспомогательные функции: форматирование текста для разных сообщений (HTML‑разметка, эмодзи), преобразование дат, выбор склонения слов («м/с» и т.п.), генерация ссылок на изображения, обработка исключений и пр.
6. **weather_handlers.py** – обработчики обновлений Telegram. Содержит функцию `on_channel_post(update: Update, context: CallbackContext)`; проверяет, содержит ли текст ключевое слово «weather», если да – инициирует создание прогнозов. Содержит функции для отправки первого набора сообщений (`send_initial_messages`) и функции‑обновления (`update_week`, `update_today`, `update_current`), вызываемые JobQueue. Здесь также реализуется сохранение `message_id` в хранилище.
7. **weather_scheduler.py** – инициализация JobQueue, планирование задач обновления. Определяет функции, которые регистрируют периодические задачи для каждого типа сообщения, используя `run_repeating` с различными интервалами (12 часов, 1 час и 10 минут)ython-telegram-bot.readthedocs.io/_/downloads/en/v13.2/pdf/#:~:text=run_once%28callback%3A%20Callable,Reference, и передают `chat_id` и `message_id` через `context`.
8. **weather_storage.py** – простая абстракция над хранением данных. Например, класс `Storage` реализует методы `load()` и `save()`, используя JSON‑файл (`weather_state.json`) для хранения списка сообщений с типом (`week`, `today`, `current`) и соответствующим `message_id`, а также отметкой времени последнего обновления.
9. **weather_requirements.txt** – перечень зависимостей проекта (python‑telegram‑bot>=21, aiohttp или httpx, pytz/zoneinfo, etc.).

При необходимости могут быть добавлены дополнительные файлы, например `weather_images` для хранения ресурсов, но основная логика укладывается в перечисленные модули.

## 5. Подробный план разработки по этапам

Ниже приведён план разработки, разделённый на этапы. Каждый этап представляет собой завершённый набор задач, которые можно выполнить последовательно. Количество этапов выбрано так, чтобы логически разделить работу и обеспечить возможность пошаговой проверки.

### Этап 1. Подготовка окружения

1. Установить Python 3.11+ и создать виртуальное окружение `venv` для проекта.
2. Создать базовую структуру папки проекта, включая перечисленные файлы (пока пустые) и папку `images` для картинок погоды.
3. Создать файл `weather_requirements.txt` со списком зависимостей: `python-telegram-bot`, `aiohttp` или `httpx`, `python-dotenv` (опционально), `zoneinfo`/`pytz` (для поддержки старых версий), `logging`, `requests` (если решите использовать вместо `httpx`), `json`, `dataclasses`.
4. Активировать виртуальное окружение и установить зависимости: `pip install -r weather_requirements.txt`.

### Этап 2. Настройка конфигурации

1. Заполнить файл `weather_config.py`: объявить константы `TELEGRAM_TOKEN`, `OWM_API_KEY`, `CEREBRAS_API_KEY`, `LAT`, `LON`, `TZ`, `UPDATE_INTERVAL_WEEK`, `UPDATE_INTERVAL_TODAY`, `UPDATE_INTERVAL_CURRENT`, `AVAILABLE_IMAGES` (словарь названий и путей к файлам) и `CHANNEL_ID`.
2. Добавить чтение переменных окружения через `os.getenv()` или библиотеку `dotenv` (если используются внешние конфигурационные файлы). Это позволит хранить токены и ключи вне исходного кода.
3. Убедиться, что список изображений соответствует реально существующим файлам в папке `images` и что их названия понятны модели Qwen.

### Этап 3. Реализация модуля работы с OpenWeatherMap – `weather_api.py`

1. Создать асинхронную функцию `fetch_weather_data(session, exclude)` – получает JSON от One Call API 3.0, принимает список частей для исключения (например, `minutely,alerts`), возвращает словарь со всеми данными. Использовать `aiohttp` (или `httpx`) для отправки запроса с параметрами `lat`, `lon`, `units=metric`, `lang=ru` и API‑ключом. Обработать исключения сети (таймауты, ошибки 4xx/5xx) и возвращать `None` при неудаче.
2. Реализовать функции `parse_current(data)` – принимает объект `current` из JSON, возвращает словарь с полями: `description`, `temp`, `feels_like`, `wind_speed`. Функция должна переводить скорость ветра в м/с (если API возвращает её в м/с – просто оставить), округлять температуру до целых значений.
3. Реализовать `parse_today(data)` – принимает массив `daily[0]` (первый день), возвращает структуру с погодой по временам суток: `morn`, `day`, `eve`, `night` (температура и feels_like), а также время `sunrise` и `sunset`. Восход должен быть перенесён на следующий день, если текущее время позже восхода (логика должна сравнивать с часовым поясом МСК). Использовать модуль `zoneinfo` для конвертации Unix‑времени в строку формата «HH:MM».
4. Реализовать `parse_week(data)` – принимает массив `daily`, создаёт список из 7 элементов, каждый содержит дату (день и месяц), минимальную и максимальную температуру, feels_like, описание и скорость ветра. Даты нужно форматировать с указанием дня недели, например «Вт, 30 июля» (можно использовать `datetime` и список русских названий дней).
5. Разработать вспомогательную функцию `format_sun_times(sunrise_ts, sunset_ts, tz)` – конвертирует UNIX‑время в строку по заданному часовому поясу. Пример: перевод времени из одной зоны в другую приведён на Stack Overflow: локализовать исходное время и вызвать `.astimezone()` для нужной зоныhttps://stackoverflow.com/questions/31977563/how-do-you-convert-a-datetime-timestamp-from-one-timezone-to-another-timezone#:~:text=1.%20Make%20two%20,import%20pytz%3B%20pytz.all_timezones.

### Этап 4. Реализация обращения к Cerebras – `weather_cerebras.py`

1. Написать асинхронную функцию `select_image(description: str, available_images: List[str], current_time: str) -> str`. Она формирует текст запроса, включающий текущее время (в формате «dd.MM.yyyy HH:MM (MSK)»), краткое описание погоды (например, «пасмурно, температура 15 °C, ветер 3 м/с») и перечисление доступных изображений. В запросе нужно чётко попросить модель вернуть **только** название картинки.
2. Собрать JSON‑объект с полями `model`, `messages`, `temperature`, `top_p`, `max_tokens`, `stream` и отправить POST‑запрос на `https://api.cerebras.ai/v1/chat/completions` с заголовками `Content-Type: application/json` и `Authorization: Bearer {CEREBRAS_API_KEY}`https://community.home-assistant.io/t/solved-cerebras-llama-3-3-token-length-150-exceeded-increase-maximum-token-to-avoid-the-issue/848932#:~:text=Don%E2%80%99t%20think%20so,works%20even%20for%20larger%20replys.
3. Получить ответ и извлечь название картинки (`response.json()['choices'][0]['message']['content']`). Добавить обработку ошибок (если модель вернёт неизвестное имя, выбрать картинку по умолчанию).

### Этап 5. Разработка утилит – `weather_utils.py`

1. Функция `create_weather_caption_current(data: dict) -> str` – формирует красивый и лаконичный текст для «погоды сейчас»: включает температуру, ощущаемую температуру, описание погоды, ветер. Использует HTML‑форматирование (`<b>`, `<i>`), эмодзи (☀️, ☁️, 🌧️) и сокращения.
2. Функция `create_weather_caption_today(data: dict) -> str` – форматирует прогноз на сегодня: выводит четыре строки (утро, день, вечер, ночь) с температурами и состоянием погоды, затем добавляет строку «Восход: HH:MM, Закат: HH:MM». Следует обрабатывать случай, когда восход уже прошёл: тогда писать «Восход завтра: HH:MM».
3. Функция `create_weather_caption_week(data_list: List[dict]) -> str` – создаёт текст на неделю: для каждого дня новую строку с датой, максимумом/минимумом, описанием. Надо избегать длинных предложений; каждую строку ограничивать ~50 символами.
4. Функция `get_day_name(date_obj)` – возвращает русское название дня недели, например «Пн».
5. Другие полезные функции: округление чисел, добавление единиц измерения, проверка существования файлов изображений.

### Этап 6. Реализация хранилища – `weather_storage.py`

1. Создать класс `Storage` с методом инициализации, принимающим путь к JSON‑файлу (например, `weather_state.json`).
2. Метод `load()` возвращает словарь с сохранёнными данными (если файл не существует – возвращает пустой словарь).
3. Метод `save(data: dict)` записывает словарь в JSON‑файл. Данные должны содержать записи вида `{ "week": {"message_id": <int>, "last_update": <timestamp>}, "today": {...}, "current": {...} }`.
4. Возможно, добавить методы `update_message_id(type, message_id)` и `get_message_id(type)` для удобства.

### Этап 7. Обработчики Telegram – `weather_handlers.py`

1. Импортировать Bot и JobQueue из `python-telegram-bot`, а также функции из `weather_api`, `weather_cerebras`, `weather_utils`, `weather_storage`.
2. Написать функцию `on_channel_post(update, context)`: она вызывается при появлении нового поста в канале. Следует проверить, что пост содержит ключевое слово «weather» (регистр не важен), и что бот имеет права администратора. Если найдено – вызвать функцию `send_initial_messages(chat_id, context)` и зарегистрировать задания в JobQueue через `weather_scheduler`.
3. Функция `send_initial_messages(chat_id, context)` выполняет следующие шаги:
    * Запрашивает данные из OpenWeatherMap (`fetch_weather_data`) c `exclude=minutely,alerts`.
    * Разбирает данные на три части: `current`, `today`, `week`.
    * Для каждого типа генерирует краткое описание и строит запрос к Cerebras для выбора изображения (`select_image`).
    * Отправляет фото с подписью через `bot.send_photo()` (параметры `chat_id`, `photo=open('images/<filename>', 'rb')`, `caption`, `parse_mode='HTML'`).
    * Сохраняет идентификатор сообщения (`message.message_id`) в хранилище (`weather_storage`) вместе с типом сообщения.
    * Планирует периодические задачи для обновления каждого сообщения (вызывает функцию планировщика).
4. Реализовать функции обновления: `update_week(context)`, `update_today(context)`, `update_current(context)`. Каждая функция выполняется планировщиком и получает через `context.job.context` данные: `chat_id`, `message_id` и тип. Шаги:
    * Запрашивает новые данные из OpenWeatherMap (можно передавать разные `exclude` – например, для текущей погоды достаточно `exclude=minutely,daily`).
    * Сравнивает новое состояние с предыдущим (опционально) и формирует текст.
    * Выбирает новую картинку через Cerebras.
    * Получает `file_id` картинки: если картинка уже отправлялась ранее, можно сохранить её `file_id` в словаре, чтобы не загружать снова; если нет – отправить её «втихую» кому‑то (например, через `sendPhoto` в другой чат) и сохранить `file_id`.
    * Вызывает `bot.edit_message_media()` для замены фотографии: формирует `InputMediaPhoto(media=file_id, caption=new_caption, parse_mode='HTML')` и передаёт `chat_id` и `message_id`. По Aiogram‑документации метод успешно заменяет изображение, но требует, чтобы файл уже был загружен и доступен на серверах Telegramhttps://docs.aiogram.dev/en/latest/api/methods/edit_message_media.html#:~:text=Use%20this%20method%20to%20edit,the%20time%20they%20were%20sent. После этого можно обновить подпись через `bot.edit_message_caption()` или сразу указать `caption` в `InputMediaPhoto`.
    * Обновляет запись в хранилище с новым временем обновления.

### Этап 8. Реализация планировщика – `weather_scheduler.py`

1. Импортировать JobQueue из `python-telegram-bot` и функции обновления из `weather_handlers`.
2. Создать функцию `schedule_updates(application, storage)` (где `application` – объект `Application` из `python-telegram-bot`). Эта функция должна считывать из хранилища `message_id` для каждого типа и регистрировать три повторяющиеся задачи через `job_queue.run_repeating()`:
    * `job_queue.run_repeating(update_week, interval=UPDATE_INTERVAL_WEEK, first=0, name='update_week', data={'chat_id': CHANNEL_ID, 'type': 'week'})`;
    * `job_queue.run_repeating(update_today, interval=UPDATE_INTERVAL_TODAY, first=0, name='update_today', data={'chat_id': CHANNEL_ID, 'type': 'today'})`;
    * `job_queue.run_repeating(update_current, interval=UPDATE_INTERVAL_CURRENT, first=0, name='update_current', data={'chat_id': CHANNEL_ID, 'type': 'current'})`.
   Здесь `data` будет храниться в `context.job.data` и передано в функции обновления. Параметр `first=0` запускает задачу сразу после регистрации.
3. Запуск планировщика должен быть выполнен после отправки исходных сообщений и сохранения их `message_id`. Если сообщений нет, планировщик не стартует.

### Этап 9. Настройка основного модуля – `weather_main.py`

1. Импортировать все необходимые модули и инициализировать логирование (`logging.basicConfig(level=logging.INFO)`).
2. Создать экземпляр `Application` (или `Updater`) библиотеки `python-telegram-bot` с токеном и параметром `concurrent_updates=True` (если требуется). Настроить `defaults` для использования HTML‑форматирования (`parse_mode=telegram.constants.ParseMode.HTML`).
3. Инициализировать хранилище (`Storage`), загрузить существующие `message_id`.
4. Зарегистрировать обработчик `ChannelPostHandler` для события `channel_post` с функцией `on_channel_post` из `weather_handlers`.
5. Запустить `application.run_polling()` или настроить webhook, если бот будет развёрнут на сервере с HTTPS.
6. После старта, если в хранилище уже сохранены `message_id`, вызвать `schedule_updates` для планирования обновлений (это позволит боту корректно перезапускаться без потери ссылок на сообщения).

### Этап 10. Тестирование бота в песочнице

1. Создать тестовый канал в Telegram и добавить туда бота (дать права администратора для публикации постов и редактирования сообщений).
2. Запустить бот локально и убедиться, что при публикации сообщения с текстом, содержащим слово «weather», он отправляет три прогноза в правильном порядке.
3. Проверить, что расписание обновлений работает: текущая погода обновляется каждые 10 минут, погода на сегодня – каждый час, на неделю – два раза в сутки. Проверить, что при обновлении картинки и подписи использован метод `editMessageMedia` и что изображения меняются корректно.
4. Проверить, что форматы сообщений лаконичны, отсутствуют переносы строк после 1024‑символьного лимита, и что описания погоды на русском языке читаемы.
5. Отследить, что восход и закат рассчитываются относительно часового пояса МСК.

### Этап 11. Улучшения и оптимизация

1. Добавить кэширование file_id изображений. Telegram выдаёт `file_id` после первой загрузки изображения; этот идентификатор можно повторно использовать, и он будет значительно быстрее, чем загрузка файла заново. Хранить `file_id` можно в `weather_storage` вместе с именем файла.
2. Реализовать обработку ошибок: если API OpenWeatherMap недоступен, бот должен поймать исключение и использовать предыдущие данные или вывести сообщение об ошибке. Аналогично – обработка исключений при обращении к Cerebras API.
3. Добавить логирование всех исключений и результатов обновления для упрощения отладки.
4. Настроить ограничения частоты вызовов (rate‑limiting). Telegram ограничивает число сообщений в секунду; библиотека `python-telegram-bot` автоматически применяет `DelayQueue`ython-telegram-bot.readthedocs.io/_/downloads/en/v13.2/pdf/#:~:text=class%20telegram,Reference, но при большом числе пользователей стоит добавить паузы.

### Этап 12. Развёртывание

1. Выбрать способ развёртывания: VPS, облачный хостинг или контейнер (Docker). Убедиться, что сервер работает постоянно и имеет доступ в интернет. При использовании Docker создать Dockerfile и docker‑compose.yml (не входят в текущий план, но могут быть добавлены).
2. Установить на сервере все зависимости, скопировать файлы проекта и запустить бот в режиме сервиса (например, systemd). Настроить перезапуск бота при сбое.
3. Обеспечить безопасность: не выкладывать API‑ключи и токены в общий доступ; использовать переменные окружения.

## 6. Важные переменные и параметры (константы)

* `TELEGRAM_TOKEN` – токен Telegram‑бота.
* `OWM_API_KEY` – ключ OpenWeatherMap.
* `CEREBRAS_API_KEY` – ключ Cerebras.
* `LAT`, `LON` – координаты района (44.600, 33.517)https://en.wikipedia.org/wiki/Gagarinsky_District,_Sevastopol#:~:text=Coordinates%3A%2044%C2%B036%E2%80%B2N%2033%C2%B031%E2%80%B2E%20%EF%BB%BF%20%2F,517.
* `TZ` – часовой пояс (Europe/Moscow). Часовой пояс определяет корректный вывод восхода/заката и локальное время.
* `UPDATE_INTERVAL_WEEK` – интервал обновления прогноза на неделю (43200 секунд = 12 часов).
* `UPDATE_INTERVAL_TODAY` – интервал обновления прогноза на сегодня (3600 секунд = 1 час).
* `UPDATE_INTERVAL_CURRENT` – интервал обновления «погоды сейчас» (600 секунд = 10 минут).
* `AVAILABLE_IMAGES` – список названий файлов изображений (например, [`sunny.jpg`, `cloudy.jpg`, `rainy.jpg`, ...]) и пути к ним. Эти названия передаются в запрос к Cerebras.
* `CHANNEL_ID` – идентификатор канала (можно использовать `@channelusername` или отрицательный числовой ID).

## 7. Заключение

Предложенный план охватывает все ключевые аспекты разработки бота: использование API OpenWeatherMap для получения данных, API Cerebras для выбора подходящих изображений, Telegram Bot API для отправки и редактирования сообщений, и библиотеку python‑telegram‑bot для реализации самого бота и планировщика задач. Следуя этому детальному плану по этапам, можно последовательно разработать и протестировать программу, обеспечивающую стабильную работу в канале: бот будет реагировать на ключевое слово, отправлять три погодных поста с нужными изображениями, а затем регулярно обновлять их в соответствии с заданной частотой.